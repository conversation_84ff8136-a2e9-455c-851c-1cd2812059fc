# file: api_server.py

import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import numpy as np
import torch
import os
import argparse
from typing import Dict, List
from contextlib import asynccontextmanager
import warnings

# 忽略 transformers 的 future 警告（可选）
warnings.filterwarnings("ignore", category=FutureWarning)

from models import TimeLLM
from utils.tools import load_content

GAS_FEATURE_ORDER = [
    'ch4_face_return_air_0308', 'ch4_upper_corner_0308', 'ch4_return_bunker_0308', 'ch4_face_0308', 'ch4_transport_bunker_0308',
    'sprayer_bottom_shaft', 'ch4_refuge_chamber_inside', 'ch4_ground_bunker_se', 'ch4_ground_belt_west', 'ch4_ground_bunker_nw',
    'ch4_ground_belt_east', 'ch4_return_bunker_0307', 'ch4_transport_face_0305', 'ch4_transport_return_air_0305',
    'ch4_rail_belt_face_5', 'ch4_rail_belt_return_air_5', 'ch4_main_return_air', 'ch4_central_return_air',
    'ch4_ground_pump_1_2', 'ch4_ground_pump_3_4', 'ch4_ground_pump_pipe', 'ch4_pump_room_electric',
    'ch4_return_bunker_0305', 'ch4_return_face_0305', 'ch4_return_return_air_0305', 'ch4_refuge_chamber_exit',
    'ch4_main_inclined_bunker', 'ch4_central_return_face', 'ch4_central_return_dev_air'
]

WEATHER_FEATURE_ORDER = [
    'p (mbar)', 'T (degC)', 'Tpot (K)', 'Tdew (degC)', 'rh (%)', 'VPmax (mbar)', 'VPact (mbar)', 'VPdef (mbar)',
    'sh (g/kg)', 'H2OC (mmol/mol)', 'rho (g/m**3)', 'wv (m/s)', 'max. wv (m/s)', 'wd (deg)', 'rain (mm)',
    'raining (s)', 'SWDR (W/m?)', 'PAR (?mol/m?/s)', 'max. PAR (?mol/m?/s)', 'Tlog (degC)', 'OT'
]

class Args(argparse.Namespace):
    def __init__(self, **kwargs):
        defaults = {'patch_len': 16, 'stride': 8, 'output_attention': False, 'n_heads': 8}
        defaults.update(kwargs)
        super().__init__(**defaults)

MODELS_CONFIG: Dict[str, dict] = {
    "gas": {
        "model_path": "./checkpoints/gas/gas_m1_120_20_30/checkpoint.pth", "feature_order": GAS_FEATURE_ORDER,
        "model": 'TimeLLM', "task_name": 'long_term_forecast', "seq_len": 120, "label_len": 20, "pred_len": 30,
        "e_layers": 2, "d_layers": 1, "factor": 1, "enc_in": 29, "dec_in": 29, "c_out": 29,
        "d_model": 32, "d_ff": 128, "llm_layers": 32, "llm_model": 'LLAMA', "llm_dim": 4096, "data": 'custom',
        "features": 'M', "target": 'ch4_face_return_air_0308', "embed": 'timeF', "root_path": "./dataset/gas/",
        "data_path": "gas_custom.csv", "patch_len": 16, "prompt_domain": True,
        "prompt_str": "The dataset records gas concentration every minute, comprising 29 indicators representing the gas concentration levels at different monitoring points.",
        "stride": 8, "output_attention": False, "dropout": 0.1
    },
    "weather": {
        "model_path": "./checkpoints/weather/weather_m10_512_48_96/checkpoint.pth", "feature_order": WEATHER_FEATURE_ORDER,
        "model": 'TimeLLM', "task_name": 'long_term_forecast', "seq_len": 512, "label_len": 48, "pred_len": 96,
        "e_layers": 2, "d_layers": 1, "factor": 3, "enc_in": 21, "dec_in": 21, "c_out": 21,
        "d_model": 32, "d_ff": 32, "llm_layers": 32, "llm_model": 'LLAMA', "llm_dim": 4096, "dropout": 0.1,
        "patch_len": 16, "prompt_domain": True, "data": 'Weather', "features": 'M', "target": 'OT', "freq": 'h',
        "embed": 'timeF', "root_path": "./dataset/weather/", "data_path": "weather.csv",
        "prompt_str": "Weather is recorded every 10 minutes for the 2020 whole year, which contains 21 meteorological indicators, such as air temperature, humidity, etc.",
        "stride": 8, "output_attention": False, "activation": "gelu"
    },
}

LOADED_MODELS: Dict[str, tuple] = {}
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

def initialize_model_and_data(model_name: str):
    config_dict = MODELS_CONFIG[model_name]
    args = Args(**config_dict)
    args.content = load_content(args)
    data_buffer = np.zeros((args.seq_len, args.enc_in))
    model_path = config_dict["model_path"]
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"找不到模型文件: {model_path}")
    model = TimeLLM.Model(args).float()
    model.load_state_dict(torch.load(model_path, map_location=DEVICE))

    # 多卡包裹
    if torch.cuda.device_count() > 1:
        print(f"🟢 检测到 {torch.cuda.device_count()} 张 GPU，启用 DataParallel 推理")
        model = torch.nn.DataParallel(model)

    model.to(DEVICE)
    model.eval()
    LOADED_MODELS[model_name] = (model, args, data_buffer)
    print(f"🎉 模型 '{model_name}' 和数据缓冲区已成功初始化！")

@asynccontextmanager
async def lifespan(app: FastAPI):
    print("服务启动 (lifespan)... 当前不预加载任何模型，首次请求时再加载。")
    yield
    print("服务正在关闭... 清理模型缓存。")
    LOADED_MODELS.clear()

app = FastAPI(
    title="智能时序预测 API (延迟加载版)",
    description="首次收到预测请求时，动态加载对应模型。",
    version="8.0.1",
    lifespan=lifespan
)

class PointData(BaseModel):
    point: str = Field(..., description="监测点名称")
    values: List[float] = Field(..., description="该监测点的一段最新序列数据")

class PredictionInput(BaseModel):
    model: str = Field(..., example="gas")
    data: List[PointData]

class PredictionOutput(BaseModel):
    model: str
    data: List[PointData]

@app.post("/predict", response_model=PredictionOutput, tags=["智能预测"])
async def predict_api(input_data: PredictionInput):
    model_name = input_data.model
    if model_name not in LOADED_MODELS:
        try:
            initialize_model_and_data(model_name)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"模型初始化失败: {e}")

    model, args, data_buffer = LOADED_MODELS[model_name]
    feature_order = MODELS_CONFIG[model_name]["feature_order"]

    try:
        input_dict = {item.point: item.values for item in input_data.data}
        if len(input_dict) != args.enc_in:
            raise ValueError(f"需要 {args.enc_in} 个监测点，收到了 {len(input_dict)} 个。")
        ordered_sequences = [input_dict[name] for name in feature_order]
        new_data_array = np.array(ordered_sequences).T
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"解析输入数据时出错: {e}")

    num_new_points = new_data_array.shape[0]
    if num_new_points >= args.seq_len:
        data_buffer[:] = new_data_array[-args.seq_len:]
    else:
        data_buffer[:-num_new_points] = data_buffer[num_new_points:]
        data_buffer[-num_new_points:] = new_data_array

    try:
        batch_x = torch.from_numpy(data_buffer).float().unsqueeze(0).to(DEVICE)
        time_mark_dim = 4
        batch_x_mark = torch.zeros((1, args.seq_len, time_mark_dim), device=DEVICE).float()
        batch_y_mark = torch.zeros((1, args.label_len + args.pred_len, time_mark_dim), device=DEVICE).float()
        dec_inp = torch.zeros((1, args.pred_len, args.c_out), device=DEVICE).float()
        dec_inp = torch.cat([batch_x[:, -args.label_len:, :], dec_inp], dim=1).float()

        with torch.no_grad():
            outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark, content=args.content)

        prediction_array = outputs.detach().cpu().numpy()[0].T
        output_data = [
            PointData(point=feature_name, values=prediction_array[i].tolist())
            for i, feature_name in enumerate(feature_order)
        ]
        return PredictionOutput(model=model_name, data=output_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"预测过程中发生内部错误: {str(e)}")

if __name__ == '__main__':
    uvicorn.run("api_server:app", host="0.0.0.0", port=8000, reload=True)
