# file: preprocess_gas_data.py
# 最终修正版：确保所有路径都基于当前脚本位置，绝对不会混淆。

import pandas as pd
import os

# --- 1. 定义清晰的路径 ---
# 获取当前脚本所在的目录，也就是我们新的、正确的 /root/autodl-tmp/Time-LLM/
project_root = os.path.dirname(os.path.abspath(__file__))

# 构造原始数据文件的绝对路径
original_file_path = os.path.join(project_root, 'dataset', 'gas', 'gas_concentration.csv')

# 构造处理后新文件的绝对路径
processed_file_path = os.path.join(project_root, 'dataset', 'gas', 'gas_custom.csv')

# --- 2. 执行数据处理 ---
print("="*80)
print(f"准备读取原始数据: {original_file_path}")
print(f"将要保存处理后数据到: {processed_file_path}")
print("="*80)

# 检查原始文件是否存在于正确的位置
if not os.path.exists(original_file_path):
    print(f"错误：在正确的位置找不到原始数据文件！")
    print(f"请确认 '{original_file_path}' 这个文件存在。")
    print("您可能需要运行 'mv' 命令将数据从 _broken 文件夹移动过来。")
    exit()

try:
    df = pd.read_csv(original_file_path, engine='python', encoding='gbk')
except Exception as e:
    print(f"错误：读取CSV文件时失败。错误信息: {e}")
    exit()

new_columns = [
    'date',
    'ch4_face_return_air_0308', 'ch4_upper_corner_0308', 'ch4_return_bunker_0308',
    'ch4_face_0308', 'ch4_transport_bunker_0308', 'sprayer_bottom_shaft',
    'ch4_refuge_chamber_inside', 'ch4_ground_bunker_se', 'ch4_ground_belt_west',
    'ch4_ground_bunker_nw', 'ch4_ground_belt_east', 'ch4_return_bunker_0307',
    'ch4_transport_face_0305', 'ch4_transport_return_air_0305', 'ch4_rail_belt_face_5',
    'ch4_rail_belt_return_air_5', 'ch4_main_return_air', 'ch4_central_return_air',
    'ch4_ground_pump_1_2', 'ch4_ground_pump_3_4', 'ch4_ground_pump_pipe',
    'ch4_pump_room_electric', 'ch4_return_bunker_0305', 'ch4_return_face_0305',
    'ch4_return_return_air_0305', 'ch4_refuge_chamber_exit', 'ch4_main_inclined_bunker',
    'ch4_central_return_face', 'ch4_central_return_dev_air'
]

if len(df.columns) != len(new_columns):
    raise ValueError(f"列数不匹配! CSV文件有 {len(df.columns)} 列, 但代码里定义了 {len(new_columns)} 列。")

df.columns = new_columns
df['date'] = pd.to_datetime(df['date'])

if df.isnull().values.any():
    print("检测到缺失值，正在填充...")
    df.fillna(method='ffill', inplace=True)
    df.fillna(0, inplace=True)
    print("缺失值填充完毕。")

df.to_csv(processed_file_path, index=False)

print("\n" + "="*80)
print(f"🎉 数据预处理成功！")
print(f"新的数据文件 'gas_custom.csv' 已保存在正确的位置: {processed_file_path}")
print("="*80)
